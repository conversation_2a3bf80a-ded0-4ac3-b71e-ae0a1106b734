import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:purchases_ui_flutter/purchases_ui_flutter.dart';
import 'package:noeji/models/enums.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/ui/screens/ideabooks_list_screen.dart';

import 'package:noeji/utils/logger.dart';

/// Widget that routes users to the appropriate screen based on their user state
/// Implements the 4-state monetization model:
/// - newUser: Shows paywall immediately
/// - trialUser/proUser: Shows main app
/// - freeUser: Shows main app with read-only access
/// - unknown: Shows loading
class UserStateRouter extends ConsumerWidget {
  const UserStateRouter({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the real-time user state
    final userStateAsync = ref.watch(realtimeUserStateProvider);

    return userStateAsync.when(
      loading: () {
        Logger.debug('UserStateRouter: User state loading, showing loading indicator');
        return const Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Loading...'),
              ],
            ),
          ),
        );
      },
      error: (error, stackTrace) {
        Logger.error('UserStateRouter: Error loading user state', error);
        // On error, default to showing the main app
        // The app will handle the error state internally
        return const IdeabooksListScreen();
      },
      data: (userState) {
        Logger.debug('UserStateRouter: User state is $userState');
        
        switch (userState) {
          case UserState.newUser:
            // New users must see paywall before accessing the app
            Logger.debug('UserStateRouter: Showing paywall for new user');
            return const PaywallScreen();
            
          case UserState.trialUser:
          case UserState.proUser:
          case UserState.freeUser:
            // These users can access the main app
            // The app will handle the differences in functionality internally
            Logger.debug('UserStateRouter: Showing main app for user state: $userState');
            return const IdeabooksListScreen();
            
          case UserState.unknown:
            // Unknown state, show loading
            Logger.debug('UserStateRouter: Unknown user state, showing loading');
            return const Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Determining user status...'),
                  ],
                ),
              ),
            );
        }
      },
    );
  }
}

/// Paywall screen for new users
/// This screen shows the RevenueCat paywall and handles the result
class PaywallScreen extends ConsumerStatefulWidget {
  const PaywallScreen({super.key});

  @override
  ConsumerState<PaywallScreen> createState() => _PaywallScreenState();
}

class _PaywallScreenState extends ConsumerState<PaywallScreen> {
  bool _isShowingPaywall = false;

  @override
  void initState() {
    super.initState();
    // Show paywall immediately when this screen is created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showPaywall();
    });
  }

  Future<void> _showPaywall() async {
    if (_isShowingPaywall) return;
    
    setState(() {
      _isShowingPaywall = true;
    });

    try {
      Logger.debug('PaywallScreen: Showing RevenueCat paywall for new user');
      final result = await RevenueCatUI.presentPaywall();

      if (!mounted) return;

      switch (result) {
        case PaywallResult.purchased:
        case PaywallResult.restored:
          Logger.debug('PaywallScreen: User completed purchase/restore, refreshing state');
          // Refresh user state after successful purchase
          final refreshUserState = ref.read(refreshUserStateProvider);
          await refreshUserState();
          // The UserStateRouter will automatically navigate to the main app
          // when the user state changes from newUser to trialUser/proUser
          break;
          
        case PaywallResult.cancelled:
          Logger.debug('PaywallScreen: User cancelled paywall');
          // User cancelled, show the paywall again after a brief delay
          await Future.delayed(const Duration(milliseconds: 500));
          if (mounted) {
            _showPaywall();
          }
          break;
          
        case PaywallResult.error:
        case PaywallResult.notPresented:
          Logger.error('PaywallScreen: Error showing paywall: $result');
          // Show error and retry
          if (mounted) {
            _showErrorAndRetry();
          }
          break;
      }
    } catch (e) {
      Logger.error('PaywallScreen: Exception showing paywall', e);
      if (mounted) {
        _showErrorAndRetry();
      }
    } finally {
      if (mounted) {
        setState(() {
          _isShowingPaywall = false;
        });
      }
    }
  }

  void _showErrorAndRetry() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: const Text('There was an error loading the subscription options. Please try again.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showPaywall();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              _isShowingPaywall 
                ? 'Loading subscription options...' 
                : 'Welcome to Noeji PRO!',
            ),
            if (!_isShowingPaywall) ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _showPaywall,
                child: const Text('Get Started'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
